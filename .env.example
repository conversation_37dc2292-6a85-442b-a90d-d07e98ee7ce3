# Change this path to the root of your akuity repository
AKUITY_ROOT=/path/to/akuity

AKP_ROOT=$AKUITY_ROOT/akuity-platform
AGENT_ROOT=$AKUITY_ROOT/agent
AKP_IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform
AGENT_SERVER_IMAGE_REPO=us-docker.pkg.dev/akuity/akp/agent-server
AGENT_IMAGE_REPO=quay.io/akuity/agent 
AGENT_BASE_IMAGE=golang:1.24

KARGO_ROOT=$AKUITY_ROOT/kargo-enterprise

GITOPS_REPO_URL=https://github.com/hanxiaop/kargo-demo.git
GITHUB_USERNAME=xxxxx
OPENAI_API_KEY=<your openai api key>
