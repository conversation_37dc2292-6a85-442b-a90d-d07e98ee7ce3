#!/bin/bash

set -ex

# Get the absolute directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source environment variables from the same directory as the script
if [ -f "$SCRIPT_DIR/.env" ]; then
    source "$SCRIPT_DIR/.env"
else
    echo ".env file not found in $SCRIPT_DIR. Please create one based on .env.example"
    exit 1
fi

# Usage refresh_kargo_image <IMAGE_TAG>
#
# Refresh the Kargo image
#   IMAGE_TAG: Tag for the image
refresh_kargo_image(){
  IMAGE_TAG=${1:-$IMAGE_TAG}
  if [ -z "$IMAGE_TAG" ]; then
    echo "Error: IMAGE_TAG must be provided, check the cluster for the kargo image tag"
    exit 1
  fi
  IMAGE_REPO=${IMAGE_REPO:-"ghcr.io/akuity/kargo"}
  IMAGE_REPO=$IMAGE_REPO IMAGE_TAG=$IMAGE_TAG make -C $KARGO_ROOT hack-build
  kubectx orbstack
  
  # Find all namespaces with kargo- prefix and restart the deployments
  echo "Restarting Kargo deployments in all kargo- namespaces..."
  for ns in $(kubectl get namespaces -o name | grep "^namespace/kargo-" | cut -d/ -f2); do
    echo "Restarting deployments in namespace: $ns"
    kubectl -n $ns rollout restart deployment/kargo-api deployment/kargo-management-controller
  done
}

# Bootstrap Akuity Platform with specified configuration
#   CUSTOMER_NUMBER: Number of customer clusters to create (default: 1)
#   AGENT_IMAGE_TAG: Optional tag for agent image
#   REFRESH_AGENT_SERVER: Whether to refresh agent server (default: false)
#   KARGO: Whether to enable Kargo instance (default: false)
bootstrap_akp(){
  CUSTOMER_NUMBER=${1:-1}
  AGENT_IMAGE_TAG=${2}
  REFRESH_AGENT_SERVER=${3:-false}
  KARGO=${4:-${KARGO:-false}}

  # Read GitHub PAT from .gh_token file
  if [ -f "$AKP_ROOT/.gh_token" ]; then
    GITHUB_PAT=$(cat "$AKP_ROOT/.gh_token")
    export GITHUB_PAT
    echo "GitHub PAT loaded from $AKP_ROOT/.gh_token"
  else
    echo "Warning: GitHub PAT file not found at $AKP_ROOT/.gh_token"
  fi
  export GITOPS_REPO_URL=${GITOPS_REPO_URL}
  export GITHUB_USERNAME=${GITHUB_USERNAME}

  kubectx orbstack

  # Check if akuity-platform deployment exists
  if kubectl get deployment -n akuity-platform &>/dev/null; then
     echo "Existing akuity-platform deployment found, using helm template approach..."
     PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make -C $AKP_ROOT image

     helm template $AKP_ROOT/charts/akuity-platform \
       --values $AKP_ROOT/charts/akuity-platform/test/values-dev.yaml \
       --set image.password=$DOCKER_PASSWORD \
       --set image.repository=us-docker.pkg.dev/akuity/akp/akuity-platform \
       --set image.tag=latest \
       --set portal.imagePullPolicy=IfNotPresent \
       --set platformController.imagePullPolicy=IfNotPresent \
       --namespace akuity-platform \
       | kubectl apply -f -
  else
    echo "No existing akuity-platform deployment found, running dev-up..."
    USE_ORBSTACK=true make -C $AKP_ROOT dev-up
  fi
  kubectx orbstack
  ## deploy aims-server
  kubectl apply -f $SCRIPT_DIR/aims/
  kubectl -n akuity-platform patch configmap portal-server --type merge --patch-file /dev/stdin << EOF
{
  "data": {
    "FEATURES_SOURCE": "dev",
    "CLUSTER_INFO_SYNC_PERIOD": "10s",
    "RESOURCES_SYNC_PERIOD": "10s"
  }
}
EOF

  if [ -n "$OPENAI_API_KEY" ]; then
    kubectl -n akuity-platform patch secret akuity-platform --type merge --patch-file /dev/stdin << EOF
{
  "data": {
    "OPENAI_API_KEY": "${OPENAI_API_KEY}"
  }
}
EOF
    echo "Added OPENAI_API_KEY to akuity-platform secret"
  else
    echo "Warning: OPENAI_API_KEY environment variable not set"
  fi

  kubectl -n akuity-platform patch configmap platform-controller --type merge --patch-file /dev/stdin << EOF
{
  "data": {
    "FEATURES_SOURCE": "dev",
    "CLUSTER_INFO_SYNC_PERIOD": "10s",
    "RESOURCES_SYNC_PERIOD": "10s"
  }
}
EOF

  # patch to akuity-platform secret in akuity-platform namespace
  
  kubectl \
   -n akuity-platform \
   rollout restart deployment/portal-server
  kubectl \
   -n akuity-platform \
   rollout restart deployment/platform-controller
  kubectl \
   -n akuity-platform \
   rollout restart deployment/aims-server

  ## delete all the cronjobs in the akuity-platform namespace since their imagepullpolicy is Always and they will pull the latest image and cannot use the local image
  kubectl delete cronjob --all -n akuity-platform

  # wait for akuity-platform to be ready
  kubectl rollout status deployment/aims-server -n akuity-platform --timeout=300s
  kubectl rollout status deployment/portal-server -n akuity-platform --timeout=300s
  kubectl rollout status deployment/platform-controller -n akuity-platform --timeout=300s

  # create organization
  if ! command -v akputil &> /dev/null; then
    make -C $AKP_ROOT akputil
    mv $AKP_ROOT/dist/akputil $GOPATH/bin/
  fi

  kubectl port-forward -n akuity-platform svc/postgres 5432:5432 &

  akputil organization create test-org --owner <EMAIL>
  API_KEY_OUTPUT=$(akputil organization create-api-key --org-name test-org --description "random-key-$(date +%s)")
  API_KEY_ID=$(echo "$API_KEY_OUTPUT" | grep -E "^\s+[a-z0-9]{16}" | awk '{print $1}')
  API_KEY_SECRET=$(echo "$API_KEY_OUTPUT" | grep -E "^\s+[a-z0-9]{16}" | awk '{print $2}')

  # Export environment variables
  export AKUITY_API_KEY_ID="$API_KEY_ID"
  export AKUITY_API_KEY_SECRET="$API_KEY_SECRET"
  export AKUITY_SERVER_URL="https://portal-server.akuity-platform"

  LATEST_VERSION=$(curl -s http://portal-server.akuity-platform/api/v1/system/cd/versions -L -k | \
    jq -r '.argocdVersions[] | select(.securityAdvisories | length == 0) | select(.akVersions | length > 0) | .akVersions[0].version' | head -1)
  
  LATEST_AGENT_VERSION=$(curl -s http://portal-server.akuity-platform/api/v1/system/agent/versions -L -k | \
    jq -r '.agentVersions[-1]')
  
  LATEST_KARGO_VERSION=$(curl -s http://portal-server.akuity-platform/api/v1/system/kargo/versions -L -k | \
    jq -r '.kargoVersions[0].version')
  
  if [ -z "$LATEST_VERSION" ] || [ -z "$LATEST_AGENT_VERSION" ] || [ -z "$LATEST_KARGO_VERSION" ]; then
    echo "Failed to fetch latest versions, falling back to original YAML files"
    echo "ArgoCD version: $LATEST_VERSION, Agent version: $LATEST_AGENT_VERSION, Kargo version: $LATEST_KARGO_VERSION"
    ARGOCD_YAML="$SCRIPT_DIR/manifests/argocd.yaml"
    ARGOCD_RESOURCES_YAML="$SCRIPT_DIR/manifests/argocd_resources.yaml"
    KARGO_YAML="$SCRIPT_DIR/manifests/kargo.yaml"
  else
    echo "Latest ArgoCD version: $LATEST_VERSION"
    echo "Latest agent version: $LATEST_AGENT_VERSION"
    echo "Latest Kargo version: $LATEST_KARGO_VERSION"
    
    ARGOCD_YAML="/tmp/argocd-updated.yaml"
    ARGOCD_RESOURCES_YAML="/tmp/argocd_resources-updated.yaml"
    KARGO_YAML="/tmp/kargo-updated.yaml"
    
    sed -E -e "s/version: v[0-9.]*-ak\.[0-9]*/version: $LATEST_VERSION/g" \
           -e "s/targetVersion: [0-9]+\.[0-9]+\.[0-9]+/targetVersion: $LATEST_AGENT_VERSION/g" \
      $SCRIPT_DIR/manifests/argocd.yaml > $ARGOCD_YAML
    
    sed -E -e "s/version: v[0-9.]*-ak\.[0-9]*/version: $LATEST_VERSION/g" \
           -e "s/targetVersion: [0-9]+\.[0-9]+\.[0-9]+/targetVersion: $LATEST_AGENT_VERSION/g" \
      $SCRIPT_DIR/manifests/argocd_resources.yaml > $ARGOCD_RESOURCES_YAML
    
    sed -E -e "s/version: v[0-9.]+-unstable-[0-9]+/version: $LATEST_KARGO_VERSION/g" \
           -e "s/targetVersion: [0-9]+\.[0-9]+\.[0-9]+/targetVersion: $LATEST_AGENT_VERSION/g" \
      $SCRIPT_DIR/manifests/kargo.yaml > $KARGO_YAML
    
    echo "Created updated YAML files with ArgoCD version: $LATEST_VERSION, agent version: $LATEST_AGENT_VERSION, and Kargo version: $LATEST_KARGO_VERSION"
  fi

  if ! command -v akuity &> /dev/null; then
    echo "Error: akuity CLI not found in PATH"
    echo "Please run the following commands in akuity-platform repo to build and install it:"
    echo "  make cli"
    echo "  akuity cli is installed in dist/akuity-cli/xxxx-version/xxx/akuity, and move it to $GOPATH/bin/"
    echo "Then run this script again."
    exit 1
  fi

  akuity argocd apply -f $ARGOCD_YAML --insecure-skip-tls-verify --org-name test-org
    # Wait for ArgoCD instance to be healthy
  echo "Waiting for ArgoCD instance to be healthy..."
  while true; do
    ARGOCD_HEALTH=$(akuity argocd instance list --org-name test-org --insecure-skip-tls-verify | awk 'NR>1 && NF>0 {print $3}' | head -1)
    if [ "$ARGOCD_HEALTH" = "Healthy" ]; then
      echo "ArgoCD instance is healthy"
      break
    fi
    echo "ArgoCD instance health: $ARGOCD_HEALTH (waiting...)"
    sleep 5
  done
  if [ "$CUSTOMER_NUMBER" -gt 1 ]; then
   for i in $(seq 2 "$CUSTOMER_NUMBER"); do
     add_customer_cluster "$i"
   done
  else
    if ! kubectx k3d-akuity-customer &>/dev/null; then
      add_customer_cluster "1"
    fi
  fi
  kubectx k3d-akuity-customer
  akuity argocd cluster get-agent-manifests --org-name test-org --instance-name test-argocd test-cluster --insecure-skip-tls-verify | kubectl apply -f -
  kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=argocd-application-controller -n akuity --timeout=300s
  kubectx orbstack
  
  envsubst < $ARGOCD_RESOURCES_YAML | akuity argocd apply -f - --insecure-skip-tls-verify --org-name test-org

  # Extract ArgoCD instance ID and export it
  REMOTE_ARGOCD=$(akuity argocd instance list --org-name test-org --insecure-skip-tls-verify | awk 'NR>1 && NF>0 {print $1}' | head -1)
  export REMOTE_ARGOCD
  echo "Exported REMOTE_ARGOCD: $REMOTE_ARGOCD"

  # Add ArgoCD URL to /etc/hosts if not already present
  if ! grep -q "${REMOTE_ARGOCD}.cd.portal-server.akuity-platform" /etc/hosts; then
    echo "127.0.0.1 ${REMOTE_ARGOCD}.cd.portal-server.akuity-platform" | sudo tee -a /etc/hosts
    echo "Added ArgoCD URL to /etc/hosts"
  fi

  if [ "$KARGO" != "true" ]; then
    echo "Kargo deployment is disabled (KARGO != true)"
    echo "ArgoCD setup completed successfully!"
    return
  fi

  envsubst < $KARGO_YAML | akuity kargo apply -f - --insecure-skip-tls-verify --org-name test-org
  # Wait for Kargo instance to be healthy
  echo "Waiting for Kargo instance to be healthy..."
  while true; do
    KARGO_HEALTH=$(akuity kargo instance list --org-name test-org --insecure-skip-tls-verify | awk 'NR>1 && NF>0 {print $3}' | head -1)
    if [ "$KARGO_HEALTH" = "Healthy" ]; then
      echo "Kargo instance is healthy"
      break
    fi
    echo "Kargo instance health: $KARGO_HEALTH (waiting...)"
    sleep 5
  done

  

  KARGO_INSTANCE=$(akuity kargo instance list --org-name test-org --insecure-skip-tls-verify | awk 'NR>1 && NF>0 {print $1}' | head -1)
  export KARGO_INSTANCE
  echo "Exported KARGO_INSTANCE: $KARGO_INSTANCE"

  echo "Getting Kargo agent ID..."
  KARGO_AGENT_ID=$(akuity kargo agent list --org-name test-org --insecure-skip-tls-verify --instance-name test-kargo | awk 'NR>1 && NF>0 {print $1}' | head -1)
  if [ -n "$KARGO_AGENT_ID" ]; then
    echo "Found Kargo agent ID: $KARGO_AGENT_ID"
    echo "Updating Kargo manifest with defaultShardAgent and reapplying..."
    
    cp $KARGO_YAML /tmp/kargo-updated-temp.yaml
    sed -i '' "/kargoInstanceSpec:/a\\
\\    defaultShardAgent: ${KARGO_AGENT_ID}
" /tmp/kargo-updated-temp.yaml
    cat /tmp/kargo-updated-temp.yaml
    envsubst < /tmp/kargo-updated-temp.yaml | akuity kargo apply -f - --insecure-skip-tls-verify --org-name test-org
    echo "Successfully updated Kargo resource with defaultShardAgent: $KARGO_AGENT_ID"
    rm -f /tmp/kargo-updated-temp.yaml
  else
    echo "Warning: Could not extract Kargo agent ID"
  fi

  # Add ArgoCD and Kargo URLs to /etc/hosts
  if ! grep -q "${KARGO_INSTANCE}.kargo.portal-server.akuity-platform" /etc/hosts; then
    echo "127.0.0.1 ${KARGO_INSTANCE}.kargo.portal-server.akuity-platform" | sudo tee -a /etc/hosts
    echo "Added Kargo URL to /etc/hosts"
  fi

   ## if image tag is specified refresh agent image
  if [ -n "$AGENT_IMAGE_TAG" ]; then
    refresh_agent_images "$AGENT_IMAGE_TAG" "$CUSTOMER_NUMBER" "$REFRESH_AGENT_SERVER"
  fi
  
  if [ -n "$LATEST_VERSION" ] && [ -n "$LATEST_AGENT_VERSION" ] && [ -n "$LATEST_KARGO_VERSION" ]; then
    rm -f /tmp/argocd-updated.yaml /tmp/argocd_resources-updated.yaml /tmp/kargo-updated.yaml
    echo "Cleaned up temporary YAML files"
  fi
}

# Usage: add_customer_cluster CLUSTER_NUMBER
#
# Add a new customer cluster with specified number
#   CLUSTER_NUMBER: The number to append to customer cluster name
add_customer_cluster(){
  if [ "$1" = "1" ]; then
    cd $AKP_ROOT && ./hack/add-customer.sh customer
  else
    cd $AKP_ROOT && ./hack/add-customer.sh customer-"$1"
  fi
}

# Usage: refresh_akp_image [IMAGE_TAG]
#
# Rebuild and refresh the Akuity Platform image
# For quick development iterations, prefer refresh_akp_bin instead
#   IMAGE_TAG: Tag for the image (default: latest)
refresh_akp_image(){
  IMAGE_TAG=${1:-latest}
  TAG=$IMAGE_TAG IMAGE_REPO=$AKP_IMAGE_REPO make -C $AKP_ROOT image
  kubectx orbstack
  kubectl rollout restart deployment/platform-controller -n akuity-platform
  kubectl rollout restart deployment/portal-server -n akuity-platform
  kubectl rollout restart deployment/aims-server -n akuity-platform
  ## delete all the cronjobs in the akuity-platform namespace since their imagepullpolicy is Always and they will pull the latest image and cannot use the local image
  kubectl delete cronjob --all -n akuity-platform
}

# Usage: refresh_akp_bin [UI] [IMAGE_TAG]
#
# Rebuild Akuity Platform binaries and optionally UI
# Recommended for fast development iterations
#   UI: Whether to rebuild UI (default: false)
#   IMAGE_TAG: Tag for the image (default: latest)
refresh_akp_bin(){
  UI=${1:-false}
  IMAGE_TAG=${2:-latest}
  kubectx orbstack
  cd $AKP_ROOT

  # Check current layer count and rebuild base if too deep
  if docker image inspect "$AKP_IMAGE_REPO:$IMAGE_TAG" >/dev/null 2>&1; then
    CURRENT_LAYERS=$(docker inspect "$AKP_IMAGE_REPO:$IMAGE_TAG" --format='{{len .RootFS.Layers}}')
    echo "Current image has $CURRENT_LAYERS layers"

    if [ "$CURRENT_LAYERS" -gt 120 ]; then
      echo "🚨 Layer count ($CURRENT_LAYERS) exceeds 120! Rebuilding base image first..."
      echo "Running 'make image' to create fresh base..."
      TAG=$IMAGE_TAG IMAGE_REPO=$AKP_IMAGE_REPO make image
      echo "✅ Fresh base image created. Proceeding with refresh..."
    fi
  fi

  GOOS=linux CGO_ENABLED=0 go build -cover -v -gcflags="all=-N -l" -o dist/ ./cmd/akuity-platform
  GOOS=linux CGO_ENABLED=0 go build -cover -v -gcflags="all=-N -l" -o dist/ ./cmd/akputil
  echo 'dlv --listen=:2345 --headless=true --api-version=2 attach $(pidof akuity-platform)' > $AKP_ROOT/dist/debugger
  cat > $AKP_ROOT/dist/build-replace.Dockerfile << EOL
  FROM golang:alpine AS build-env
  RUN go install github.com/go-delve/delve/cmd/dlv@latest
  RUN go install github.com/imwithye/kubedlv@v1.0.1
  FROM us-docker.pkg.dev/akuity/akp/akuity-platform
  COPY dist/debugger /usr/bin/debugger
  COPY models/liquibase/changelogs /changelogs
  COPY --from=build-env /go/bin/dlv /usr/bin
  COPY --from=build-env /go/bin/kubedlv /usr/bin
  COPY dist/akuity-platform /bin/akuity-platform
  COPY dist/akputil /bin/akputil
EOL
  if [ "$UI" = true ]; then
    cd $AKP_ROOT/portal/ui && NODE_ENV='production' pnpm run build && cp -r build $AKP_ROOT/dist/ && cd $AKP_ROOT
    cd $AKP_ROOT/aims/ui && NODE_ENV='production' pnpm run build && cp -r build $AKP_ROOT/dist/aims-build && cd $AKP_ROOT
    cat >> $AKP_ROOT/dist/build-replace.Dockerfile << EOL
  COPY dist/build /portal/ui/build
  COPY portal/ui/extensions-build /portal/ui/extensions-build
  COPY portal/ui/kargo-extensions-build /portal/ui/kargo-extensions-build
  COPY dist/aims-build /aims/ui/build
EOL
  fi

  docker build -t "$AKP_IMAGE_REPO:$IMAGE_TAG" -f $AKP_ROOT/dist/build-replace.Dockerfile .
  kubectl rollout restart deployment/platform-controller -n akuity-platform
  kubectl rollout restart deployment/portal-server -n akuity-platform
  kubectl rollout restart deployment/aims-server -n akuity-platform
  ## delete all the cronjobs in the akuity-platform namespace since their imagepullpolicy is Always and they will pull the latest image and cannot use the local image
  kubectl delete cronjob --all -n akuity-platform
}

# Usage: refresh_agent_images [IMAGE_TAG] [CUSTOMER_NUMBER] [REFRESH_AGENT_SERVER]
#
# Refresh agent images across all customer clusters
#   IMAGE_TAG: Tag for the image (default: latest)
#   CUSTOMER_NUMBER: Number of customer clusters (default: 1)
#   REFRESH_AGENT_SERVER: Whether to refresh agent server (default: false)
refresh_agent_images(){
  IMAGE_TAG=${1:-latest}
  CUSTOMER_NUMBER=${2:-1}
  REFRESH_AGENT_SERVER=${3:-false}
  if [ "$REFRESH_AGENT_SERVER" = true ]; then
    refresh_agent_server_image "$IMAGE_TAG"
  fi
  IMAGE_TAG=$IMAGE_TAG AGENT_IMAGE_REPO=$AGENT_IMAGE_REPO BASE_IMAGE=$AGENT_BASE_IMAGE make -C $AGENT_ROOT agent-image
  for i in $(seq 1 "$CUSTOMER_NUMBER"); do
    if [ "$i" -eq 1 ]; then
      k3d image import "$AGENT_IMAGE_REPO:$IMAGE_TAG" -c akuity-customer
      kubectx k3d-akuity-customer
    else
      k3d image import "$AGENT_IMAGE_REPO:$IMAGE_TAG" -c akuity-customer-"$i"
      kubectx k3d-akuity-customer-"$i"
    fi
    kubectl delete pods --all -n akuity
  done

  kubectx orbstack
}

# Usage: refresh_agent_server_image [IMAGE_TAG]
#
# Refresh the agent server image
#   IMAGE_TAG: Tag for the image (default: latest)
refresh_agent_server_image(){
  IMAGE_TAG=${1:-latest}
  IMAGE_TAG=$IMAGE_TAG AGENT_SERVER_IMAGE_REPO=$AGENT_SERVER_IMAGE_REPO make -C $AGENT_ROOT agent-server-image
  # Restart agent-server pods in argocd namespaces to pick up the new image
  kubectx orbstack
  for ns in $(kubectl get ns -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | grep '^argocd-'); do
    if kubectl get deployment agent-server -n "$ns" &>/dev/null; then
      echo "Restarting agent-server in namespace: $ns"
      kubectl rollout restart deployment/agent-server -n "$ns"
    fi
  done
}

# Usage: tear_down_akp [CUSTOMER_NUMBER]
#
# Tear down Akuity Platform and customer clusters
#   CUSTOMER_NUMBER: Number of customer clusters to tear down (default: 1)
tear_down_akp(){
  CUSTOMER_NUMBER=${1:-1}
  USE_ORBSTACK=true make -C $AKP_ROOT dev-down
  for i in $(seq 2 "$CUSTOMER_NUMBER"); do
    k3d cluster delete akuity-customer-"$i"
  done
}
