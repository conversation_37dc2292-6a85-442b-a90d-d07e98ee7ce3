#!/bin/bash

show_help() {
    echo "Usage: ./stop-akp.sh [CUSTOMER_NUMBER]"
    echo "Tear down Akuity Platform and customer clusters"
    echo ""
    echo "Arguments:"
    echo "  CUSTOMER_NUMBER    Number of customer clusters to tear down (default: 1)"
    echo ""
    echo "Options:"
    echo "  -h, --help        Show this help message"
    echo ""
    echo "This will:"
    echo "  1. Tear down the development environment"
    echo "  2. Delete all additional customer clusters"
    echo ""
    exit 0
}

# Check for help flags first, before sourcing utils.sh
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_help
fi

source "$(dirname "$0")/utils.sh"
tear_down_akp "$1"