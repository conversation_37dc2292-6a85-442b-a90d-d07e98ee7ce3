# KubeVision Scripts

A collection of utility scripts to help with developing features and local testing for KubeVision.

## Overview

These scripts provide a streamlined workflow for:
- Setting up local development environments
- Managing multiple customer clusters
- Fast Refreshing components with new changes during development

## Prerequisites

### Required Tools:
   - kubectl
   - [orbstack](https://orbstack.dev/)
   - k3d (for local Kubernetes clusters)
   - [kubectx](https://github.com/ahmetb/kubectx)
   - make

### Environment Setup
To use these scripts, ensure that this repository is placed alongside the akuity-platform and agent repositories, sharing the same root folder.

```
$AKUITY_ROOT/
├── akuity-platform/      # Platform repository
├── agent/                # Agent repository
└── kubevision-scripts/   # This repository
```

Copy the `.env.example` to `.env` and edit it with your own setup:
```bash
# Copy the example environment file
cp kubevision-scripts/.env.example kubevision-scripts/.env
  
# Edit the environment variables
vim kubevision-scripts/.env
```

Example configuration in `.env`:
```bash
AKUITY_ROOT=/Users/<USER>/projects/akuity
AKP_ROOT=$AKUITY_ROOT/akuity-platform
AGENT_ROOT=$AKUITY_ROOT/agent
AKP_IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform
AGENT_SERVER_IMAGE_REPO=us-docker.pkg.dev/akuity/akp/agent-server
AGENT_IMAGE_REPO=quay.io/akuity/agent 
AGENT_BASE_IMAGE=golang:1.22
```

## Scripts

### start-akp.sh

Set up the initial development environment and creates customer clusters. Only needed for first-time setup or when you need a completely fresh environment.

```bash
# Basic usage
./kubevision-scripts/start-akp.sh

# Create multiple customer clusters
./kubevision-scripts/start-akp.sh 3
```

For day-to-day development, you'll mainly use these refresh scripts:

### refresh-akp.sh

Update platform components with new code changes during development. Supports two modes:
- Binary refresh (faster for development)
- Full image rebuild

```bash
# Quick binary refresh (recommended for development)
./kubevision-scripts/refresh-akp.sh

# Refresh with UI rebuild
MODE=bin UI=true ./kubevision-scripts/refresh-akp.sh

# Full image rebuild (use when testing final changes)
MODE=image ./kubevision-scripts/refresh-akp.sh
```

### refresh-agent-image.sh

Update agent images with new agent code changes across all customer clusters.

```bash
# Basic refresh
./kubevision-scripts/refresh-agent-image.sh

# Refresh specific version
./kubevision-scripts/refresh-agent-image.sh 0.5.47

# Refresh across multiple clusters
./kubevision-scripts/refresh-agent-image.sh 0.5.47 3 

# Refresh agent server as well
./kubevision-scripts/refresh-agent-image.sh 0.5.47 3 true
```

### stop-akp.sh

Completely tears down the development environment and removes customer clusters. Only use when you need to reset everything or clean up resources.

```bash
# Tear down akp
./kubevision-scripts/stop-akp.sh

# Tear down akp and multiple customer clusters
./kubevision-scripts/stop-akp.sh 3
```