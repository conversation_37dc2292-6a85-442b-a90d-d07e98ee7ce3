#!/bin/bash

show_help() {
    echo "Usage: ./start-akp.sh [CUSTOMER_NUMBER] [AGENT_IMAGE_TAG] [REFRESH_AGENT_SERVER]"
    echo "Bootstrap Akuity Platform with specified configuration"
    echo ""
    echo "Arguments:"
    echo "  CUSTOMER_NUMBER      Number of customer clusters to create (default: 1)"
    echo "  AGENT_IMAGE_TAG      Optional tag for agent image"
    echo "  REFRESH_AGENT_SERVER Whether to refresh agent server (default: false)"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "This will:"
    echo "  1. Set up the development environment"
    echo "  2. Configure portal-server and platform-controller"
    echo "  3. Create specified number of customer clusters"
    echo "  4. Optionally refresh agent images if AGENT_IMAGE_TAG is provided"
    echo ""
    exit 0
}

# Check for help flags first, before sourcing utils.sh
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_help
fi

source "$(dirname "$0")/utils.sh"
bootstrap_akp "$1" "$2" "$3"