#!/bin/bash

show_help() {
    echo "Usage: ./refresh-kargo.sh [IMAGE_TAG]"
    echo "Refresh the Kargo image"
    echo ""
    echo "Arguments:"
    echo "  IMAGE_TAG            Tag for the kargo image (check the cluster for the kargo image tag)"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo ""
    exit 0
}

# Check for help flags first, before sourcing utils.sh
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_help
fi

source "$(dirname "$0")/utils.sh"
refresh_kargo_image "$1"
