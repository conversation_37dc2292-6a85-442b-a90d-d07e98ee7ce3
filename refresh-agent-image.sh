#!/bin/bash

show_help() {
    echo "Usage: ./refresh-agent-image.sh [IMAGE_TAG] [CUSTOMER_NUMBER] [REFRESH_AGENT_SERVER]"
    echo "Refresh agent images across all customer clusters"
    echo ""
    echo "Arguments:"
    echo "  IMAGE_TAG            Tag for the agent image (default: latest)"
    echo "  CUSTOMER_NUMBER      Number of customer clusters to refresh (default: 1)"
    echo "  REFRESH_AGENT_SERVER Whether to refresh agent server (default: false)"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo ""
    exit 0
}

# Check for help flags first, before sourcing utils.sh
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_help
fi

source "$(dirname "$0")/utils.sh"
refresh_agent_images "$1" "$2" "$3"
