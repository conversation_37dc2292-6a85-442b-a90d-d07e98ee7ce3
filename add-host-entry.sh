#!/bin/bash

# 提取 argocd- 命名空间的 ID
ns_id=$(kubectl get ns -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | grep '^argocd-' | sed 's/^argocd-//')

# 构造要添加的域名
hostname="${ns_id}.cd.portal-server.akuity-platform"
entry="127.0.0.1 ${hostname}"

# 删除旧的包含 cd.portal-server.akuity-platform 的行
sudo sed -i.bak '/cd\.portal-server\.akuity-platform/d' /etc/hosts

# 添加新条目
echo "$entry" | sudo tee -a /etc/hosts > /dev/null

echo "已更新 /etc/hosts，添加: $entry"
