apiVersion: kargo.akuity.io/v1alpha1
kind: Kargo
metadata:
  name: test-kargo
spec:
  description: ""
  kargoInstanceSpec:
    akuityIntelligence:
      enabled: true
      aiSupportEngineerEnabled: true
      allowedGroups:
      - admin
      allowedUsernames:
      - admin
  version: v1.7.0-unstable-********
---
apiVersion: v1
data:
  adminAccountEnabled: "true"
  adminAccountTokenTtl: 24h
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/component: api
    app.kubernetes.io/instance: kargo
    app.kubernetes.io/name: kargo
  name: kargo-cm
---
apiVersion: v1
data:
  adminAccountPasswordHash: "$2a$10$8l.rKcNZA19lh52n48bX/eo5bIUKP8rVjfE9dOdjHa8kajY9sCVRu"
kind: Secret
metadata:
  labels:
    app.kubernetes.io/component: api
    app.kubernetes.io/instance: kargo
    app.kubernetes.io/name: kargo
  name: kargo-secret
---
apiVersion: kargo.akuity.io/v1alpha1
kind: KargoAgent
metadata:
  name: test-agent
  namespace: akuity
spec:
  data:
    akuityManaged: true
    autoUpgradeDisabled: false
    kustomization: null
    remoteArgocd: ${REMOTE_ARGOCD}
    size: small
    targetVersion: 0.5.60
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Project
metadata:
  name: kargo-demo
---
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  name: kargo-demo-repo
  namespace: kargo-demo
  labels:
    kargo.akuity.io/cred-type: git
stringData:
  repoURL: ${GITOPS_REPO_URL}
  username: ${GITHUB_USERNAME}
  password: ${GITHUB_PAT}
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Warehouse
metadata:
  name: kargo-demo
  namespace: kargo-demo
spec:
  subscriptions:
  - image:
      repoURL: public.ecr.aws/nginx/nginx
      semverConstraint: ^1.26.0
      discoveryLimit: 5
---
apiVersion: kargo.akuity.io/v1alpha1
kind: PromotionTask
metadata:
  name: demo-promo-process
  namespace: kargo-demo
spec:
  vars:
  - name: gitopsRepo
    value: ${GITOPS_REPO_URL}
  - name: imageRepo
    value: public.ecr.aws/nginx/nginx
  steps:
  - uses: git-clone
    as: clone
    config:
      repoURL: ${{ vars.gitopsRepo }}
      checkout:
      - branch: main
        path: ./src
      - branch: stage/${{ ctx.stage }}
        create: true
        path: ./out
  - uses: git-clear
    as: clear
    config:
      path: ./out
  - uses: kustomize-set-image
    as: update
    config:
      path: ./src/base
      images:
      - image: ${{ vars.imageRepo }}
        tag: ${{ imageFrom(vars.imageRepo).Tag }}
  - uses: kustomize-build
    as: build
    config:
      path: ./src/stages/${{ ctx.stage }}
      outPath: ./out
  - uses: git-commit
    as: commit
    config:
      path: ./out
      message: ${{ task.outputs.update.commitMessage }}
  - uses: git-push
    as: push
    config:
      path: ./out
  - uses: argocd-update
    as: argocd-update
    config:
      apps:
      - name: kargo-demo-${{ ctx.stage }}
        sources:
        - repoURL: ${{ vars.gitopsRepo }}
          desiredRevision: ${{ task.outputs.commit.commit }}
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: test
  namespace: kargo-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: kargo-demo
    sources:
      direct: true
  promotionTemplate:
    spec:
      steps:
      - task:
          name: demo-promo-process
        as: promo-process
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: uat
  namespace: kargo-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: kargo-demo
    sources:
      stages:
      - test
  promotionTemplate:
    spec:
      steps:
      - task:
          name: demo-promo-process
        as: promo-process
---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: prod
  namespace: kargo-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: kargo-demo
    sources:
      stages:
      - uat
  promotionTemplate:
    spec:
      steps:
      - task:
          name: demo-promo-process
        as: promo-process
