apiVersion: argocd.akuity.io/v1alpha1
kind: ArgoCD
metadata:
  name: test-argocd
spec:
  description: ""
  instanceSpec:
    akuityIntelligenceExtension:
      enabled: true
      aiSupportEngineerEnabled: true
      allowedGroups:
      - admin
      allowedUsernames:
      - admin
    declarativeManagementEnabled: true
    multiClusterK8sDashboardEnabled: true
    kubeVisionConfig:
      aiConfig:
        incidents: {}
      cveScanConfig:
        rescanInterval: 8h
  version: v3.0.6-ak.57
---
apiVersion: argocd.akuity.io/v1alpha1
kind: Cluster
metadata:
  name: test-cluster
  namespace: akuity
spec:
  data:
    multiClusterK8sDashboardEnabled: true
    size: small
    targetVersion: 0.5.60
---
apiVersion: v1
data:
  accounts.admin: login
  exec.enabled: "false"
  ga.anonymizeusers: "false"
  helm.enabled: "true"
  kustomize.enabled: "true"
  server.rbac.log.enforce.enable: "false"
  statusbadge.enabled: "false"
  ui.bannerpermanent: "false"
  users.anonymous.enabled: "false"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-cm
---
apiVersion: v1
data:
  admin.password: JDJhJDEwJDZ3dFJPRWg3OEpOdWpldG4zblZ2aHVRNUxLZXdlZ1d1eTRHMnZHUWhnU1VCaS84NGxCUnpX
kind: Secret
metadata:
  name: argocd-secret
  namespace: argocd
type: Opaque
