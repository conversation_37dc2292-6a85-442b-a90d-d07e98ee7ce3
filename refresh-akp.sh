#!/bin/bash

show_help() {
    echo "Usage: ./refresh-akp.sh [options]"
    echo "Refresh AKP system components by rebuilding binaries or images"
    echo ""
    echo "Options:"
    echo "  -h, --help    Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  MODE         Set refresh mode: "
    echo "               'bin'  - Rebuild binaries (default)"
    echo "               'image'- Rebuild and refresh the Akuity Platform image"
    echo ""
    echo "When MODE=bin:"
    echo "  UI          Whether to rebuild UI components (default: false)"
    echo ""
    echo "Note: Both modes will restart the platform-controller and portal-server deployments"
    echo ""
    exit 0
}

# Check for help flags first, before sourcing utils.sh
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_help
fi

source "$(dirname "$0")/utils.sh"
REFRESH_MODE=${MODE:-"bin"}
REFRESH_UI=${UI:-false}

# Always ensure portal UI dependencies are up to date before refreshing
if [ -z "$AKP_ROOT" ]; then
  echo "AKP_ROOT is not set; cannot run pnpm install for portal/ui" >&2
else
  if [ -d "$AKP_ROOT/portal/ui" ]; then
    (cd "$AKP_ROOT/portal/ui" && pnpm i)
  else
    echo "Directory $AKP_ROOT/portal/ui does not exist; skipping pnpm install" >&2
  fi
fi

if [ "$REFRESH_MODE" == "bin" ]; then
  refresh_akp_bin "$REFRESH_UI"
elif [ "$REFRESH_MODE" == "image" ]; then
  refresh_akp_image
fi
