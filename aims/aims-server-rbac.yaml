apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: aims-server
rules:
- apiGroups:
  - ''
  resources:
  - configmaps
  verbs:
  - get
  - write
  - update
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: aims-server
  namespace: akuity-platform
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: aims-server
subjects:
  - kind: ServiceAccount
    name: aims-server
