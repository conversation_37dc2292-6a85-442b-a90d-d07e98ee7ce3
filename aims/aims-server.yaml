apiVersion: v1
kind: ServiceAccount
metadata:
  name: aims-server
  namespace: akuity-platform
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: aims-server
rules:
  - apiGroups:
      - ''
    resources:
      - configmaps
    verbs:
      - get
      - write
      - update
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: aims-server
  namespace: akuity-platform
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: aims-server
subjects:
  - kind: ServiceAccount
    name: aims-server
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aims-server
  namespace: akuity-platform
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: aims-server
  template:
    metadata:
      labels:
        app.kubernetes.io/name: aims-server
    spec:
      serviceAccountName: aims-server
      containers:
        - name: aims-server
          command: [akuity-platform, aims-server]
          image: us-docker.pkg.dev/akuity/akp/akuity-platform:latest
          imagePullPolicy: Never
          ports:
            - containerPort: 9095
              name: http
          envFrom:
            - configMapRef:
                name: portal-server
            - secretRef:
                name: akuity-platform
          resources:
            limits:
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 500Mi
      imagePullSecrets:
        - name: akuity-pullsecrets
---
apiVersion: v1
kind: Service
metadata:
  name: aims
  namespace: akuity-platform
  labels:
    app.kubernetes.io/name: aims-server
spec:
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: http
  selector:
    app.kubernetes.io/name: aims-server
